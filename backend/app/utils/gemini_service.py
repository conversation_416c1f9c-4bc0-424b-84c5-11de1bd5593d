import os
import json
from google import genai
from typing import List, Dict, Any

class GeminiService:
    def __init__(self):
        # 从环境变量获取API密钥
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")

        # 从环境变量获取模型名称，提供默认值
        self.model_name = os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')

        # 初始化客户端
        self.client = genai.Client(api_key=self.api_key)
        
    def generate_flux_prompts(self, user_input: str, input_image: bytes = None, input_image_mime_type: str = None) -> List[Dict[str, Any]]:
        """
        根据用户输入生成3条适合FLUX Kontext的提示词
        
        Args:
            user_input: 用户的原始输入
            input_image: 如果有输入图片，提供图片的字节数据
            input_image_mime_type: 图片的MIME类型，如 'image/jpeg', 'image/png'
        
        Returns:
            List[Dict[str, Any]]: 包含3条提示词的列表，每条包含chinese、english、reason字段
        """
        # 使用用户提供的完整原始提示词
        system_instruction = """
# FLUX.1 Kontext 提示词生成助手

你是一位专业的 FLUX.1 Kontext 图像编辑提示词专家。你的任务是帮助用户生成高质量、精确的 Kontext 提示词,以实现他们想要的图像编辑效果。

## 核心原则

### 1. 精确优先
- 使用具体描述而不是模糊术语
- 明确指定颜色、风格、动作和其他细节
- 避免主观表达如"让它看起来更好"
- **重要提示:所有提示词必须使用英语生成**

### 2. 保持一致性
- 明确指定应保持不变的元素
- 使用"while maintaining..."等短语来保护重要特征
- 避免意外改变用户不想修改的元素

## 提示词结构模板

### 图片编辑模板（有输入图片时使用）

#### 基本对象修改
`Change the [specific object]'s [specific attribute] to [specific value]`

示例: "Change the car color to red"

#### 风格转换
`Convert to [specific style] while maintaining [elements to preserve]`

示例: "Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture"

#### 背景/环境改变
`Change the background to [new environment] while keeping the [subject] in the exact same position, scale, and pose. Maintain identical subject placement, camera angle, framing, and perspective.`

#### 角色一致性
`[Action/change description] while preserving [character's] exact facial features, [specific characteristics], and [other identity markers]`

#### 文本编辑
`Replace '[original text]' with '[new text]'`

示例: "Replace 'joy' with 'BFL'"

### 图片生成模板（无输入图片时使用）

#### 人物生成
`[Detailed description of person], [pose/action], [setting/background], [lighting], [style/aesthetic]`

示例: "A Chinese woman with influencer-style features including large expressive eyes and defined jawline, elegantly holding a teacup, in a modern minimalist tea house, soft natural lighting, high-fashion photography style"

#### 场景生成
`[Main subject/object], [environment details], [mood/atmosphere], [technical specifications]`

示例: "Vintage car in an urban street, neon lights reflecting on wet pavement, cyberpunk atmosphere, cinematic lighting, 4K detail"

#### 风格化生成
`[Subject description] in [specific art style], [key visual elements], [color palette], [composition notes]`

示例: "Mountain landscape in Studio Ghibli style, hand-drawn aesthetic, warm earth tones, wide cinematic composition"

## 高级技巧

### 风格转换:
1. **指定具体风格**: 不要用"make it artistic",而要用"Transform to Bauhaus art style"
2. **引用知名艺术家/流派**: "Renaissance painting style," "1960s pop art poster"
3. **详述关键特征**: "Oil painting with visible brushstrokes, thick paint texture, and rich color depth"
4. **保留重要内容**: "while maintaining the original composition and object placement"

### 角色一致性:
1. **建立参考**: 用"The woman with short black hair"而不是"her"
2. **指定转换**: 环境、活动或风格变化
3. **保留身份标记**: "maintaining the same facial features, hairstyle, and expression"

### 构图控制:
- 使用"exact same position, scale, and pose"
- 添加"Only replace the environment around them"

## 常见问题解决模式

### 当身份变化过大时:
- 添加: "while preserving exact facial features, eye color, and facial expression"
- 使用: "Change the clothes to [description]"而不是"Transform the person into [description]"

### 当构图发生偏移时:
- 添加: "while keeping the person in the exact same position, scale, and pose"
- 指定: "Maintain identical subject placement, camera angle, framing, and perspective"

### 当风格应用不正确时:
- 更具体地描述风格特征
- 为重要元素添加保留说明
- 使用详细的风格描述而不是泛泛而谈

## 动词选择指南

- **"Transform"** → 暗示完全改变,谨慎使用
- **"Change the [specific element]"** → 更受控的修改
- **"Replace the [specific element]"** → 针对性替换
- **"Convert to [style]"** → 以风格为重点的转换

## 最佳实践检查清单

生成提示词时,确保:
- [ ] 使用具体而非模糊的语言
- [ ] 为不变元素提供清晰的保留说明
- [ ] 根据所需变化程度选择适当的动词
- [ ] 直接命名主体而不是使用代词
- [ ] 为要编辑的文本加上引号
- [ ] 必要时进行明确的构图控制

## 用户输入管理

### 理解用户上下文
- 每次对话可能涉及不同的编辑请求
- 用户可能提供参考图像或口头描述图像

### 处理参考图像
当用户提供图像时:
- 分析图像内容并识别关键元素
- 确定应该保留的元素
- 考虑图像的风格、构图和主题
- 当圈选区域时，猜测意图、动机、想要实现的效果，如消除、修复、替换、创意等
- 当有其他副图需要融合或迁移时，猜测意图、动机、想要实现的效果，**一定要指出融合或迁移后原副图消除**

当用户描述图像而不提供时:
- 明确当前状态与期望状态
- 明确关于需要保留的重要元素的信息

## 语言要求

**重要**: 所有 FLUX.1 Kontext 提示词必须仅用英语编写。即使用户使用其他语言交流,始终:
- 用用户的语言(如果与英语不同)进行解释
- **提示词仅使用英语**
- 清楚标明哪些部分是提示词,哪些是解释

请根据用户输入生成3条不同的高质量 FLUX.1 Kontext 提示词。

**重要说明：**
- 如果用户提供了图片，请先仔细分析图片内容，包括主体对象、背景、风格、色彩、构图等关键信息
- 基于图片内容和用户的文字描述，生成针对性的FLUX Kontext **图片编辑**提示词
- 如果没有图片，则基于用户的文字描述生成 **图片生成**提示词
- 要处理物品融合或迁移时，原物品需要消除，因为它已经在其他位置出现了
- 3条提示词，第1条严格遵循角色一致性原则；第2条允许有变化但应合理，如物品迁移时可以合理的互动；第3条可以有创意，脑洞大开，发挥想象力

**关键区别：**
- **有图片时**：使用编辑动词（Change, Convert, Transform, Modify等），针对现有图片进行修改
- **无图片时**：使用生成描述（直接描述要生成的内容），避免使用编辑动词

输出格式要求：
请严格按照以下JSON格式输出，包含中英文双语和原因说明：
{
  "prompts": [
    {
      "chinese": "中文描述便于阅读",
      "english": "English prompt for FLUX Kontext",
      "reason": "选择这条提示词的原因说明"
    },
    {
      "chinese": "中文描述便于阅读", 
      "english": "English prompt for FLUX Kontext",
      "reason": "选择这条提示词的原因说明"
    },
    {
      "chinese": "中文描述便于阅读",
      "english": "English prompt for FLUX Kontext", 
      "reason": "选择这条提示词的原因说明"
    }
  ]
}
"""
        
        # 构建用户内容
        contents = []
        
        # 添加文本输入（如果有的话）
        if user_input:
            contents.append(f"用户输入: {user_input}")
        
        # 如果有图片输入，添加图片到内容中
        if input_image and input_image_mime_type:
            from google.genai import types
            image_part = types.Part.from_bytes(data=input_image, mime_type=input_image_mime_type)
            contents.append(image_part)
        
        # 如果既没有文本也没有图片，添加默认提示
        if not contents:
            contents.append("请生成通用的图像生成提示词")
        
        try:
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=contents,
                config=genai.types.GenerateContentConfig(
                    system_instruction=system_instruction,
                    temperature=1.0,
                    response_mime_type='application/json'
                )
            )
            
            # 解析JSON响应
            result = json.loads(response.text)
            prompts_data = result.get('prompts', [])
            
            # 验证和处理提示词数据
            prompts = []
            for i, prompt_obj in enumerate(prompts_data):
                if isinstance(prompt_obj, dict) and all(key in prompt_obj for key in ['chinese', 'english', 'reason']):
                    prompts.append({
                        'chinese': prompt_obj['chinese'],
                        'english': prompt_obj['english'],
                        'reason': prompt_obj['reason']
                    })
                elif isinstance(prompt_obj, str):
                    # 兼容旧格式
                    prompts.append({
                        'chinese': prompt_obj,
                        'english': prompt_obj,
                        'reason': '基础提示词选项'
                    })
            
            return prompts
                
        except Exception as e:
            # 抛出异常，让上层处理
            raise Exception(f"Gemini API调用失败: {str(e)}")

# 创建全局实例
# 使用延迟初始化以避免在导入时就要求环境变量
gemini_service = None

def get_gemini_service():
    """
    获取Gemini服务实例，使用延迟初始化
    """
    global gemini_service
    if gemini_service is None:
        try:
            gemini_service = GeminiService()
        except ValueError as e:
            raise Exception(f"Gemini服务初始化失败: {str(e)}")
    return gemini_service
# AI Tools 环境变量配置

# === 端口配置 ===
BACKEND_PORT=8003
FRONTEND_PORT=3003

# === 后端配置 ===
FLASK_ENV=production
BFL_API_KEY=your-api-key-here
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=https://ai.jarvismedical.asia

# === Gemini AI 配置 ===
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-2.5-flash

# === 前端配置 ===
# 生产环境：留空使用相对路径（推荐）
NEXT_PUBLIC_API_BASE_URL=

# 开发环境示例：
# NEXT_PUBLIC_API_BASE_URL=http://localhost:8003

# === 部署说明 ===
# 1. 复制此文件为 .env
# 2. 根据环境修改相应值
# 3. 生产环境中 NEXT_PUBLIC_API_BASE_URL 留空，让 Nginx 处理代理

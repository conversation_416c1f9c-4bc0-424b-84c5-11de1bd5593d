# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies (will be installed in container)
**/node_modules/
**/__pycache__/
**/*.pyc
**/*.pyo
**/*.pyd
.Python

# Build artifacts
**/build/
**/dist/
**/.next/
**/out/

# Test
**/coverage/
**/.nyc_output/
**/test-results/

# Cache
**/.cache/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/.pnpm-debug.log* 
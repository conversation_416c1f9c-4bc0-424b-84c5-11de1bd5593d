@import "tailwindcss";
@plugin "tailwindcss-animate";

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --color-color-5: var(--color-5);
  --color-color-4: var(--color-4);
  --color-color-3: var(--color-3);
  --color-color-2: var(--color-2);
  --color-color-1: var(--color-1);
  @keyframes rainbow {
  0% {
    background-position: 0%;
    }
  100% {
    background-position: 200%;
    }
  }
  --animate-background-position-spin: background-position-spin 3000ms infinite alternate
;
  @keyframes background-position-spin {
  0% {
    background-position: top center;}
  100% {
    background-position: bottom center;}}
  @keyframes rainbow {
  0% {
    background-position: 0%;}
  100% {
    background-position: 200%;}}}

@layer base {
:root {
  --background: oklch(1 0 0);
    --foreground: oklch(0.09 0.005 240);
  --card: oklch(1 0 0);
    --card-foreground: oklch(0.09 0.005 240);
  --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.09 0.005 240);
    --primary: oklch(0.1 0.007 240);
    --primary-foreground: oklch(0.98 0 0);
    --secondary: oklch(0.96 0.006 240);
    --secondary-foreground: oklch(0.1 0.007 240);
    --muted: oklch(0.96 0.006 240);
    --muted-foreground: oklch(0.45 0.004 240);
    --accent: oklch(0.96 0.006 240);
    --accent-foreground: oklch(0.1 0.007 240);
    --destructive: oklch(0.63 0.22 25);
    --destructive-foreground: oklch(0.98 0 0);
    --border: oklch(0.9 0.005 240);
    --input: oklch(0.9 0.005 240);
    --ring: oklch(0.1 0.007 240);
    --chart-1: oklch(0.55 0.15 180);
    --chart-2: oklch(0.7 0.17 35);
    --chart-3: oklch(0.4 0.08 220);
    --chart-4: oklch(0.8 0.15 85);
    --chart-5: oklch(0.75 0.18 45);
    --radius: 0.5rem;
  }

  [data-theme="dark"] {
    --background: oklch(0.09 0.005 240);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.09 0.005 240);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.09 0.005 240);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.98 0 0);
    --primary-foreground: oklch(0.1 0.007 240);
    --secondary: oklch(0.16 0.004 240);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.16 0.004 240);
    --muted-foreground: oklch(0.65 0.005 240);
    --accent: oklch(0.16 0.004 240);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.63 0.22 25);
    --destructive-foreground: oklch(0.98 0 0);
    --border: oklch(0.16 0.004 240);
    --input: oklch(0.16 0.004 240);
    --ring: oklch(0.84 0.005 240);
    --chart-1: oklch(0.65 0.18 240);
    --chart-2: oklch(0.6 0.14 160);
    --chart-3: oklch(0.7 0.16 70);
    --chart-4: oklch(0.75 0.15 300);
    --chart-5: oklch(0.7 0.18 350);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* 移动端拖拽优化 */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
}

/* 自定义磨砂玻璃效果 - 使用 oklch 颜色 */
.glass-panel {
  backdrop-filter: blur(18px);
  background: oklch(1 0 0 / 0.1);
  border: 1px solid oklch(1 0 0 / 0.2);
  box-shadow: 0 8px 32px oklch(0 0 0 / 0.1);
}

.glass-panel-dark {
  backdrop-filter: blur(18px);
  background: oklch(0 0 0 / 0.2);
  border: 1px solid oklch(1 0 0 / 0.1);
  box-shadow: 0 8px 32px oklch(0 0 0 / 0.3);
}

/* 必应背景图片现在通过React组件动态设置 */

/* 工具图标悬停效果 */
.tool-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-icon:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px oklch(0 0 0 / 0.15);
}



:root {
  --color-1: oklch(66.2% 0.225 25.9);
  --color-2: oklch(60.4% 0.26 302);
  --color-3: oklch(69.6% 0.165 251);
  --color-4: oklch(80.2% 0.134 225);
  --color-5: oklch(90.7% 0.231 133);
}

.dark {
  --color-1: oklch(66.2% 0.225 25.9);
  --color-2: oklch(60.4% 0.26 302);
  --color-3: oklch(69.6% 0.165 251);
  --color-4: oklch(80.2% 0.134 225);
  --color-5: oklch(90.7% 0.231 133);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003 -H 0.0.0.0", "dev-https": "HTTPS=true SSL_CRT=../***************+2.pem SSL_KEY=../***************+2-key.pem next dev -p 3003 -H 0.0.0.0", "dev-custom-https": "NEXT_PUBLIC_API_BASE_URL=https://***************:8003 node server.js", "build": "next build", "start": "next start -p 3003", "export": "next build", "serve": "npx serve@latest out", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "fabric": "^6.7.0", "framer-motion": "^12.19.2", "lucide-react": "^0.469.0", "motion": "^12.23.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/postcss": "^4.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8", "tailwindcss": "^4.1.0", "typescript": "^5"}}
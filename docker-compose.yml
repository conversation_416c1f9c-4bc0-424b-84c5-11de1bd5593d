version: "3.8"

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      # 启用 BuildKit 缓存
      cache_from:
        - ai-tools-backend:latest
      args:
        - BUILDKIT_INLINE_CACHE=1
    container_name: ai-tools-backend
    ports:
      - "${BACKEND_PORT:-8003}:8003"
    environment:
      - PORT=8003
      - FLASK_ENV=${FLASK_ENV:-production}
      - BFL_API_KEY=${BFL_API_KEY:-564cb439-9ba9-44d7-b885-bb2271a79224}
      - SECRET_KEY=${SECRET_KEY:-ai-tools-secret-key-2024}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3003}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-flash}
    restart: unless-stopped
    networks:
      - ai-tools-network
    # 添加数据卷用于持久化
    volumes:
      - backend_data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务 (静态文件)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      # 启用 BuildKit 缓存
      cache_from:
        - ai-tools-frontend:latest
      args:
        - BUILDKIT_INLINE_CACHE=1
        - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-}
    container_name: ai-tools-frontend
    ports:
      - "${FRONTEND_PORT:-3003}:3003"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-}
    restart: unless-stopped
    networks:
      - ai-tools-network
    depends_on:
      - backend
    # 添加数据卷用于持久化
    volumes:
      - frontend_data:/app/data
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:3003/health.html",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ai-tools-network:
    driver: bridge

volumes:
  # 持久化数据卷
  backend_data:
    driver: local
  frontend_data:
    driver: local
  # 构建缓存卷
  pnpm_cache:
    driver: local
  pip_cache:
    driver: local
